# Authentication System Documentation

## Overview

This Android application implements a secure authentication system for the Ariel ticketing system. The authentication is designed with production-level security considerations.

## Architecture

### Components

1. **AuthManager**: Singleton class managing authentication state
2. **AuthRepository**: Handles authentication API calls and data management
3. **SecurePreferences**: Encrypted storage for sensitive data
4. **NetworkClient**: HTTP client with automatic authentication headers
5. **LoginActivity**: User interface for authentication

### Security Features

- **Encrypted Storage**: Uses `EncryptedSharedPreferences` with AES256_GCM encryption
- **Automatic Token Management**: Tokens are automatically added to API requests
- **Network Security**: Configured for HTTPS in production, allows HTTP for development
- **Input Validation**: Proper validation of user credentials
- **Error Handling**: Comprehensive error handling with user-friendly messages

## Configuration

### Environment URLs

- **Development**: `http://10.0.2.2:8000/` (Android Emulator)
- **Production**: `https://tk.rahavardit.ir/`

URLs are automatically selected based on build type using `BuildConfig`.

### API Endpoints

- **Authentication**: `POST /api/token/`
  - Request: `{"username": "user", "password": "pass"}`
  - Response: `{"token": "...", "id": 1, "username": "user", "is_superuser": false, "is_limited_admin": false}`

## Usage

### Login Process

1. User enters credentials in `LoginActivity`
2. `AuthManager.login()` is called
3. Token and user data are securely stored
4. User is redirected to `MainActivity`

### Making Authenticated API Calls

```kotlin
// Get API service with automatic authentication
val apiService = ApiClient.getApiService(context)

// Make authenticated requests
val response = apiService.someAuthenticatedEndpoint()
```

### Checking Authentication Status

```kotlin
val authManager = AuthManager.getInstance(context)

// Check if user is logged in
if (authManager.isLoggedIn.value == true) {
    // User is authenticated
}

// Get current user
authManager.currentUser.observe(this) { user ->
    user?.let {
        // Handle user data
    }
}
```

### Logout

```kotlin
authManager.logout()
// User will be redirected to LoginActivity
```

## Security Considerations

1. **Token Storage**: Tokens are encrypted using Android Keystore
2. **Network Security**: HTTPS enforced in production
3. **Certificate Pinning**: Can be added for additional security
4. **Request Logging**: Only enabled in debug builds
5. **Input Sanitization**: All user inputs are validated

## Testing

To test the authentication system:

1. Run the app in debug mode (uses development server)
2. Enter valid credentials from your Django backend
3. Verify successful login and token storage
4. Test logout functionality
5. Verify automatic token inclusion in API requests

## Troubleshooting

### Common Issues

1. **Network Error**: Check if development server is running on `http://10.0.2.2:8000/`
2. **Invalid Credentials**: Verify username/password with Django backend
3. **Token Issues**: Clear app data to reset stored tokens

### Debug Information

Enable logging in debug builds to see network requests and responses.

## Implementation Summary

### What's Been Added

1. **Complete Authentication System**
   - Secure token storage with EncryptedSharedPreferences
   - Automatic token injection in API requests
   - Login/logout functionality
   - Session management

2. **Security Features**
   - AES256_GCM encryption for stored data
   - Network security configuration
   - Certificate pinning support (for production)
   - Biometric authentication support
   - Input validation and sanitization

3. **UI Components**
   - SplashActivity for initial authentication check
   - LoginActivity with Material Design
   - Updated MainActivity with logout functionality
   - User information display in FirstFragment

4. **Network Layer**
   - Retrofit with OkHttp
   - Automatic authentication headers
   - Comprehensive error handling
   - Environment-specific URLs (dev/prod)

5. **Data Management**
   - Repository pattern for data access
   - LiveData for reactive UI updates
   - Singleton pattern for managers

### Files Created/Modified

**New Files:**
- `AuthManager.kt` - Main authentication manager
- `AuthRepository.kt` - Data repository for auth operations
- `SecurePreferences.kt` - Encrypted storage
- `NetworkClient.kt` - HTTP client with auth
- `LoginActivity.kt` - Login UI
- `SplashActivity.kt` - Initial screen
- `BiometricHelper.kt` - Biometric authentication
- `SessionManager.kt` - Session management
- `ErrorHandler.kt` - Error handling utilities
- Model classes for API requests/responses

**Modified Files:**
- `MainActivity.kt` - Added auth checks and logout
- `FirstFragment.kt` - Display user information
- `AndroidManifest.xml` - Permissions and activities
- `build.gradle.kts` - Dependencies and build config
- `strings.xml` - UI strings

### Next Steps

1. **Test the authentication flow**
2. **Add your actual API endpoints**
3. **Configure certificate pinning for production**
4. **Add biometric authentication to login flow**
5. **Implement proper session timeout handling**
