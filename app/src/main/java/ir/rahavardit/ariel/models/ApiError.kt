package ir.rahavardit.ariel.models

import com.google.gson.annotations.SerializedName

data class ApiError(
    @SerializedName("detail")
    val detail: String? = null,
    @SerializedName("error")
    val error: String? = null,
    @SerializedName("message")
    val message: String? = null,
    @SerializedName("non_field_errors")
    val nonFieldErrors: List<String>? = null
) {
    fun getErrorMessage(): String {
        return detail 
            ?: error 
            ?: message 
            ?: nonFieldErrors?.firstOrNull() 
            ?: "Unknown error occurred"
    }
}
