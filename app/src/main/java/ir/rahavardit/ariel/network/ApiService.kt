package ir.rahavardit.ariel.network

import ir.rahavardit.ariel.models.AuthRequest
import ir.rahavardit.ariel.models.AuthResponse
import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST

interface ApiService {
    @POST("api/token/")
    suspend fun authenticate(@Body authRequest: AuthRequest): Response<AuthResponse>

    // Example authenticated endpoint - replace with your actual endpoints
    @GET("api/user/profile/")
    suspend fun getUserProfile(): Response<AuthResponse>

    // Add more API endpoints here as needed
}
