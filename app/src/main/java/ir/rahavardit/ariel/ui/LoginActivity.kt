package ir.rahavardit.ariel.ui

import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.Toast
import androidx.appcompat.app.AppCompatActivity
import androidx.lifecycle.lifecycleScope
import ir.rahavardit.ariel.MainActivity
import ir.rahavardit.ariel.R
import ir.rahavardit.ariel.data.AuthManager
import ir.rahavardit.ariel.databinding.ActivityLoginBinding
import kotlinx.coroutines.launch
import java.io.IOException

class LoginActivity : AppCompatActivity() {
    
    private lateinit var binding: ActivityLoginBinding
    private lateinit var authManager: AuthManager
    
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLoginBinding.inflate(layoutInflater)
        setContentView(binding.root)
        
        authManager = AuthManager.getInstance(this)
        
        // Check if user is already logged in
        if (authManager.isLoggedIn.value == true) {
            navigateToMain()
            return
        }
        
        setupClickListeners()
    }
    
    private fun setupClickListeners() {
        binding.btnLogin.setOnClickListener {
            val username = binding.etUsername.text.toString().trim()
            val password = binding.etPassword.text.toString().trim()
            
            if (validateInput(username, password)) {
                performLogin(username, password)
            }
        }
    }
    
    private fun validateInput(username: String, password: String): Boolean {
        if (username.isEmpty() || password.isEmpty()) {
            showError(getString(R.string.invalid_credentials))
            return false
        }
        return true
    }
    
    private fun performLogin(username: String, password: String) {
        setLoading(true)
        hideError()
        
        lifecycleScope.launch {
            try {
                val result = authManager.login(username, password)
                
                if (result.isSuccess) {
                    Toast.makeText(this@LoginActivity, getString(R.string.login_success), Toast.LENGTH_SHORT).show()
                    navigateToMain()
                } else {
                    val error = result.exceptionOrNull()
                    val errorMessage = when {
                        error is IOException -> getString(R.string.network_error)
                        error?.message != null -> error.message!!
                        else -> getString(R.string.login_failed)
                    }
                    showError(errorMessage)
                }
            } catch (e: Exception) {
                showError(getString(R.string.network_error))
            } finally {
                setLoading(false)
            }
        }
    }
    
    private fun setLoading(isLoading: Boolean) {
        binding.progressBar.visibility = if (isLoading) View.VISIBLE else View.GONE
        binding.btnLogin.isEnabled = !isLoading
        binding.etUsername.isEnabled = !isLoading
        binding.etPassword.isEnabled = !isLoading
    }
    
    private fun showError(message: String) {
        binding.tvError.text = message
        binding.tvError.visibility = View.VISIBLE
    }
    
    private fun hideError() {
        binding.tvError.visibility = View.GONE
    }
    
    private fun navigateToMain() {
        val intent = Intent(this, MainActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }
}
