package ir.rahavardit.ariel.data

import android.content.Context
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import ir.rahavardit.ariel.models.AuthResponse
import ir.rahavardit.ariel.models.User
import ir.rahavardit.ariel.network.NetworkClient

class AuthManager private constructor(context: Context) {

    companion object {
        @Volatile
        private var INSTANCE: AuthManager? = null

        fun getInstance(context: Context): AuthManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: AuthManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }

    private val securePreferences = SecurePreferences(context)
    private val networkClient = NetworkClient.getInstance(context)
    private val authRepository = AuthRepository(securePreferences, networkClient)

    private val _isLoggedIn = MutableLiveData<Boolean>()
    val isLoggedIn: LiveData<Boolean> = _isLoggedIn

    private val _currentUser = MutableLiveData<User?>()
    val currentUser: LiveData<User?> = _currentUser

    init {
        updateAuthState()
    }

    suspend fun login(username: String, password: String): Result<AuthResponse> {
        val result = authRepository.login(username, password)
        updateAuthState()
        return result
    }

    fun logout() {
        authRepository.logout()
        updateAuthState()
    }

    fun getAuthToken(): String? {
        return authRepository.getAuthToken()
    }

    private fun updateAuthState() {
        val loggedIn = authRepository.isLoggedIn()
        _isLoggedIn.value = loggedIn
        _currentUser.value = if (loggedIn) authRepository.getCurrentUser() else null
    }
}
