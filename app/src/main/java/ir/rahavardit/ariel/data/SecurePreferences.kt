package ir.rahavardit.ariel.data

import android.content.Context
import android.content.SharedPreferences
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey

class SecurePreferences(context: Context) {
    
    companion object {
        private const val PREFS_NAME = "ariel_secure_prefs"
        private const val KEY_AUTH_TOKEN = "auth_token"
        private const val KEY_USER_ID = "user_id"
        private const val KEY_USERNAME = "username"
        private const val KEY_IS_SUPERUSER = "is_superuser"
        private const val KEY_IS_LIMITED_ADMIN = "is_limited_admin"
    }
    
    private val masterKey = MasterKey.Builder(context)
        .setKeyScheme(MasterKey.KeyScheme.AES256_GCM)
        .build()
    
    private val sharedPreferences: SharedPreferences = EncryptedSharedPreferences.create(
        context,
        PREFS_NAME,
        masterKey,
        EncryptedSharedPreferences.PrefKeyEncryptionScheme.AES256_SIV,
        EncryptedSharedPreferences.PrefValueEncryptionScheme.AES256_GCM
    )
    
    fun saveAuthToken(token: String) {
        sharedPreferences.edit().putString(KEY_AUTH_TOKEN, token).apply()
    }
    
    fun getAuthToken(): String? {
        return sharedPreferences.getString(KEY_AUTH_TOKEN, null)
    }
    
    fun saveUserData(id: Int, username: String, isSuperuser: Boolean, isLimitedAdmin: Boolean) {
        sharedPreferences.edit()
            .putInt(KEY_USER_ID, id)
            .putString(KEY_USERNAME, username)
            .putBoolean(KEY_IS_SUPERUSER, isSuperuser)
            .putBoolean(KEY_IS_LIMITED_ADMIN, isLimitedAdmin)
            .apply()
    }
    
    fun getUserId(): Int {
        return sharedPreferences.getInt(KEY_USER_ID, -1)
    }
    
    fun getUsername(): String? {
        return sharedPreferences.getString(KEY_USERNAME, null)
    }
    
    fun isSuperuser(): Boolean {
        return sharedPreferences.getBoolean(KEY_IS_SUPERUSER, false)
    }
    
    fun isLimitedAdmin(): Boolean {
        return sharedPreferences.getBoolean(KEY_IS_LIMITED_ADMIN, false)
    }
    
    fun isLoggedIn(): Boolean {
        return getAuthToken() != null && getUserId() != -1
    }
    
    fun clearAll() {
        sharedPreferences.edit().clear().apply()
    }
}
