package ir.rahavardit.ariel.data

import android.content.Context
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import java.util.concurrent.TimeUnit

class SessionManager private constructor(context: Context) {
    
    companion object {
        @Volatile
        private var INSTANCE: SessionManager? = null
        private const val SESSION_TIMEOUT_MINUTES = 30L
        
        fun getInstance(context: Context): SessionManager {
            return INSTANCE ?: synchronized(this) {
                INSTANCE ?: SessionManager(context.applicationContext).also { INSTANCE = it }
            }
        }
    }
    
    private val securePreferences = SecurePreferences(context)
    private val scope = CoroutineScope(Dispatchers.Main + Job())
    private var sessionTimeoutJob: Job? = null
    
    fun startSession() {
        resetSessionTimeout()
    }
    
    fun extendSession() {
        resetSessionTimeout()
    }
    
    fun endSession() {
        sessionTimeoutJob?.cancel()
        securePreferences.clearAll()
    }
    
    private fun resetSessionTimeout() {
        sessionTimeoutJob?.cancel()
        sessionTimeoutJob = scope.launch {
            delay(TimeUnit.MINUTES.toMillis(SESSION_TIMEOUT_MINUTES))
            // Session expired
            endSession()
            // Notify that session expired (you can add a callback here)
        }
    }
    
    fun isSessionValid(): Boolean {
        return securePreferences.isLoggedIn() && sessionTimeoutJob?.isActive == true
    }
}
