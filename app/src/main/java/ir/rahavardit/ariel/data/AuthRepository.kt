package ir.rahavardit.ariel.data

import android.content.Context
import ir.rahavardit.ariel.models.AuthRequest
import ir.rahavardit.ariel.models.AuthResponse
import ir.rahavardit.ariel.models.User
import ir.rahavardit.ariel.network.NetworkClient
import ir.rahavardit.ariel.utils.ErrorHandler
import retrofit2.Response

class AuthRepository(
    private val securePreferences: SecurePreferences,
    private val networkClient: NetworkClient
) {

    suspend fun login(username: String, password: String): Result<AuthResponse> {
        return try {
            val authRequest = AuthRequest(username, password)
            val response: Response<AuthResponse> = networkClient.apiService.authenticate(authRequest)

            if (response.isSuccessful) {
                val authResponse = response.body()
                if (authResponse != null) {
                    // Save authentication data securely
                    securePreferences.saveAuthToken(authResponse.token)
                    securePreferences.saveUserData(
                        authResponse.id,
                        authResponse.username,
                        authResponse.isSuperuser,
                        authResponse.isLimitedAdmin
                    )
                    Result.success(authResponse)
                } else {
                    Result.failure(Exception("Empty response body"))
                }
            } else {
                val errorMessage = ErrorHandler.handleApiError(response)
                Result.failure(Exception(errorMessage))
            }
        } catch (e: Exception) {
            val errorMessage = ErrorHandler.handleNetworkError(e)
            Result.failure(Exception(errorMessage))
        }
    }

    fun logout() {
        securePreferences.clearAll()
    }

    fun isLoggedIn(): Boolean {
        return securePreferences.isLoggedIn()
    }

    fun getCurrentUser(): User? {
        return if (isLoggedIn()) {
            User(
                id = securePreferences.getUserId(),
                username = securePreferences.getUsername() ?: "",
                isSuperuser = securePreferences.isSuperuser(),
                isLimitedAdmin = securePreferences.isLimitedAdmin()
            )
        } else {
            null
        }
    }

    fun getAuthToken(): String? {
        return securePreferences.getAuthToken()
    }
}
