package ir.rahavardit.ariel

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import ir.rahavardit.ariel.data.AuthManager
import ir.rahavardit.ariel.databinding.FragmentFirstBinding
import com.google.android.material.snackbar.Snackbar

/**
 * Homepage fragment displaying dashboard boxes for different sections.
 */
class FirstFragment : Fragment() {

    private var _binding: FragmentFirstBinding? = null
    private lateinit var authManager: AuthManager

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {

        _binding = FragmentFirstBinding.inflate(inflater, container, false)
        return binding.root

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        authManager = AuthManager.getInstance(requireContext())

        // Debug: Log that the fragment is created
        android.util.Log.d("FirstFragment", "Fragment view created")

        setupUI()
        setupClickListeners()
    }

    private fun setupUI() {
        // Debug: Log that setupUI is called
        android.util.Log.d("FirstFragment", "setupUI called")

        // Set default welcome message
        binding.textviewWelcome.text = "Welcome to Ariel!"
        android.util.Log.d("FirstFragment", "Welcome text set")

        // Display personalized welcome message
        authManager.currentUser.observe(viewLifecycleOwner) { user ->
            android.util.Log.d("FirstFragment", "User observed: $user")
            if (user != null) {
                binding.textviewWelcome.text = "Welcome, ${user.username}!"

                // Show Users box only for superusers
                if (user.isSuperuser) {
                    binding.cardUsers.visibility = View.VISIBLE
                } else {
                    binding.cardUsers.visibility = View.GONE
                }
            } else {
                // Default state - hide users box for non-authenticated users
                binding.cardUsers.visibility = View.GONE
            }
        }
    }

    private fun setupClickListeners() {
        // Tickets box click
        binding.cardTickets.setOnClickListener {
            showComingSoon("Tickets")
        }

        // FAQs box click
        binding.cardFaqs.setOnClickListener {
            showComingSoon("FAQs")
        }

        // Knowledges box click
        binding.cardKnowledges.setOnClickListener {
            showComingSoon("Knowledges")
        }

        // Events box click
        binding.cardEvents.setOnClickListener {
            showComingSoon("Events")
        }

        // Profile box click
        binding.cardProfile.setOnClickListener {
            showComingSoon("Profile")
        }

        // Users box click (only visible for superusers)
        binding.cardUsers.setOnClickListener {
            showComingSoon("Users")
        }
    }

    private fun showComingSoon(section: String) {
        Snackbar.make(
            binding.root,
            "$section section coming soon!",
            Snackbar.LENGTH_SHORT
        ).show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
