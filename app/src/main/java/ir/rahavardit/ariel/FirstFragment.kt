package ir.rahavardit.ariel

import android.os.Bundle
import androidx.fragment.app.Fragment
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.navigation.fragment.findNavController
import ir.rahavardit.ariel.data.AuthManager
import ir.rahavardit.ariel.databinding.FragmentFirstBinding
import com.google.android.material.snackbar.Snackbar

/**
 * Homepage fragment displaying navigation boxes for different app sections.
 */
class FirstFragment : Fragment() {

    private var _binding: FragmentFirstBinding? = null
    private lateinit var authManager: AuthManager

    // This property is only valid between onCreateView and
    // onDestroyView.
    private val binding get() = _binding!!

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {

        _binding = FragmentFirstBinding.inflate(inflater, container, false)
        return binding.root

    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        authManager = AuthManager.getInstance(requireContext())

        setupBoxClickListeners()
        observeUserData()
    }

    private fun setupBoxClickListeners() {
        binding.cardTickets.setOnClickListener {
            // TODO: Navigate to tickets screen
            showComingSoonMessage("Tickets")
        }

        binding.cardFaqs.setOnClickListener {
            // TODO: Navigate to FAQs screen
            showComingSoonMessage("FAQs")
        }

        binding.cardKnowledges.setOnClickListener {
            // TODO: Navigate to knowledges screen
            showComingSoonMessage("Knowledges")
        }

        binding.cardEvents.setOnClickListener {
            // TODO: Navigate to events screen
            showComingSoonMessage("Events")
        }

        binding.cardProfile.setOnClickListener {
            // TODO: Navigate to profile screen
            showComingSoonMessage("Profile")
        }

        binding.cardUsers.setOnClickListener {
            // TODO: Navigate to users management screen
            showComingSoonMessage("Users")
        }
    }

    private fun observeUserData() {
        authManager.currentUser.observe(viewLifecycleOwner) { user ->
            user?.let {
                // Show/hide Users box based on superuser status
                binding.cardUsers.visibility = if (it.isSuperuser) {
                    View.VISIBLE
                } else {
                    View.GONE
                }
            }
        }
    }

    private fun showComingSoonMessage(feature: String) {
        Snackbar.make(
            binding.root,
            "$feature feature coming soon!",
            Snackbar.LENGTH_SHORT
        ).show()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
