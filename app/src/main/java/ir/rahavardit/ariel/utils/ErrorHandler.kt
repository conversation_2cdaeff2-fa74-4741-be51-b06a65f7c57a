package ir.rahavardit.ariel.utils

import com.google.gson.Gson
import ir.rahavardit.ariel.models.ApiError
import retrofit2.Response
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

object ErrorHandler {
    
    fun <T> handleApiError(response: Response<T>): String {
        return try {
            val errorBody = response.errorBody()?.string()
            if (errorBody != null) {
                val apiError = Gson().fromJson(errorBody, ApiError::class.java)
                apiError.getErrorMessage()
            } else {
                getHttpErrorMessage(response.code())
            }
        } catch (e: Exception) {
            getHttpErrorMessage(response.code())
        }
    }
    
    fun handleNetworkError(throwable: Throwable): String {
        return when (throwable) {
            is UnknownHostException -> "No internet connection"
            is SocketTimeoutException -> "Request timeout"
            is IOException -> "Network error"
            else -> throwable.message ?: "Unknown error"
        }
    }
    
    private fun getHttpErrorMessage(code: Int): String {
        return when (code) {
            400 -> "Bad request"
            401 -> "Invalid credentials"
            403 -> "Access forbidden"
            404 -> "Service not found"
            408 -> "Request timeout"
            429 -> "Too many requests"
            500 -> "Server error"
            502 -> "Bad gateway"
            503 -> "Service unavailable"
            else -> "HTTP error: $code"
        }
    }
}
