package ir.rahavardit.ariel.utils

import android.content.Context
import ir.rahavardit.ariel.data.AuthManager
import ir.rahavardit.ariel.network.ApiClient
import ir.rahavardit.ariel.network.ApiService
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import retrofit2.Response

/**
 * Helper class for making authenticated API calls
 */
class ApiHelper(private val context: Context) {
    
    private val authManager = AuthManager.getInstance(context)
    private val apiService = ApiClient.getApiService(context)
    
    /**
     * Execute an API call with automatic error handling
     */
    suspend fun <T> executeApiCall(
        apiCall: suspend (ApiService) -> Response<T>
    ): Result<T> = withContext(Dispatchers.IO) {
        try {
            // Check if user is still authenticated
            if (authManager.isLoggedIn.value != true) {
                return@withContext Result.failure(Exception("User not authenticated"))
            }
            
            val response = apiCall(apiService)
            
            if (response.isSuccessful) {
                val body = response.body()
                if (body != null) {
                    Result.success(body)
                } else {
                    Result.failure(Exception("Empty response body"))
                }
            } else {
                // Handle 401 Unauthorized - token might be expired
                if (response.code() == 401) {
                    authManager.logout()
                    Result.failure(Exception("Session expired. Please login again."))
                } else {
                    val errorMessage = ErrorHandler.handleApiError(response)
                    Result.failure(Exception(errorMessage))
                }
            }
        } catch (e: Exception) {
            val errorMessage = ErrorHandler.handleNetworkError(e)
            Result.failure(Exception(errorMessage))
        }
    }
    
    /**
     * Example usage:
     * 
     * val apiHelper = ApiHelper(context)
     * val result = apiHelper.executeApiCall { apiService ->
     *     apiService.getUserProfile()
     * }
     * 
     * result.onSuccess { userProfile ->
     *     // Handle success
     * }.onFailure { error ->
     *     // Handle error
     * }
     */
}
