<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="48dp"
    android:height="48dp"
    android:viewportWidth="48"
    android:viewportHeight="48">
    
    <!-- Question mark circle background -->
    <circle
        android:fillColor="@color/icon_faqs"
        android:cx="24"
        android:cy="24"
        android:r="20" />
    
    <!-- Question mark -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M24,14 C27.3,14 30,16.7 30,20 C30,22.2 28.8,24.1 27,25.1 L27,28 L21,28 L21,24 C21,23.4 21.4,23 22,23 C24.2,23 26,21.2 26,19 C26,16.8 24.2,15 22,15 C19.8,15 18,16.8 18,19 L12,19 C12,13.5 16.5,9 22,9 C27.5,9 32,13.5 32,19 C32,22.9 29.4,26.2 25.8,27.6 L25.8,30 L22.2,30 L22.2,26.4 C18.6,25 16,21.7 16,17.8 C16,12.3 20.5,7.8 26,7.8 C31.5,7.8 36,12.3 36,17.8 Z" />
    
    <!-- Question mark simplified -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M24,12 C28.4,12 32,15.6 32,20 C32,22.8 30.4,25.2 28,26.4 L28,30 L20,30 L20,26.4 C17.6,25.2 16,22.8 16,20 C16,15.6 19.6,12 24,12 Z M24,16 C21.8,16 20,17.8 20,20 L24,20 C24,17.8 22.2,16 20,16 Z" />
    
    <!-- Simplified question mark -->
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M20,14 C22.2,14 24,15.8 24,18 C24,19.3 23.3,20.4 22.3,21 L22,21.2 L22,24 L20,24 L20,20.5 L20.7,20.2 C21.5,19.8 22,19 22,18 C22,16.9 21.1,16 20,16 C18.9,16 18,16.9 18,18 L16,18 C16,15.8 17.8,14 20,14 Z M21,28 C21.6,28 22,28.4 22,29 C22,29.6 21.6,30 21,30 C20.4,30 20,29.6 20,29 C20,28.4 20.4,28 21,28 Z" />
    
    <!-- Final simplified version -->
    <group android:translateX="24" android:translateY="24">
        <path
            android:fillColor="#FFFFFF"
            android:pathData="M-4,-8 C-1.8,-8 0,-6.2 0,-4 C0,-2.7 -0.7,-1.6 -1.7,-1 L-2,-0.8 L-2,2 L-4,2 L-4,-1.5 L-3.3,-1.8 C-2.5,-2.2 -2,-3 -2,-4 C-2,-5.1 -2.9,-6 -4,-6 C-5.1,-6 -6,-5.1 -6,-4 L-8,-4 C-8,-6.2 -6.2,-8 -4,-8 Z M-3,6 C-2.4,6 -2,6.4 -2,7 C-2,7.6 -2.4,8 -3,8 C-3.6,8 -4,7.6 -4,7 C-4,6.4 -3.6,6 -3,6 Z" />
    </group>
</vector>
