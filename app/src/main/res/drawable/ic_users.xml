<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="48dp"
    android:height="48dp"
    android:viewportWidth="48"
    android:viewportHeight="48">
    
    <!-- Users background -->
    <circle
        android:fillColor="@color/icon_users"
        android:cx="24"
        android:cy="24"
        android:r="20" />
    
    <!-- First person -->
    <circle
        android:fillColor="#FFFFFF"
        android:cx="18"
        android:cy="16"
        android:r="4" />
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M18,22 C14,22 11,24 11,27 L11,32 L25,32 L25,27 C25,24 22,22 18,22 Z" />
    
    <!-- Second person (overlapping) -->
    <circle
        android:fillColor="#E0E0E0"
        android:cx="30"
        android:cy="18"
        android:r="4" />
    <path
        android:fillColor="#E0E0E0"
        android:pathData="M30,24 C26,24 23,26 23,29 L23,34 L37,34 L37,29 C37,26 34,24 30,24 Z" />
    
    <!-- Overlap correction -->
    <circle
        android:fillColor="#FFFFFF"
        android:cx="30"
        android:cy="18"
        android:r="3.5" />
    <path
        android:fillColor="#FFFFFF"
        android:pathData="M30,23.5 C26.5,23.5 24,25.5 24,28.5 L24,33 L36,33 L36,28.5 C36,25.5 33.5,23.5 30,23.5 Z" />
</vector>
