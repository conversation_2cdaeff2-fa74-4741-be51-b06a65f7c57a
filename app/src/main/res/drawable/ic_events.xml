<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="48dp"
    android:height="48dp"
    android:viewportWidth="48"
    android:viewportHeight="48">
    
    <!-- Calendar background -->
    <path
        android:fillColor="@color/icon_events"
        android:pathData="M10,12 L38,12 Q40,12 40,14 L40,38 Q40,40 38,40 L10,40 Q8,40 8,38 L8,14 Q8,12 10,12 Z" />
    
    <!-- Calendar header -->
    <rect
        android:fillColor="#C2185B"
        android:x="8"
        android:y="12"
        android:width="32"
        android:height="6"
        android:rx="2" />
    
    <!-- Calendar rings -->
    <rect
        android:fillColor="#666666"
        android:x="14"
        android:y="8"
        android:width="2"
        android:height="8"
        android:rx="1" />
    <rect
        android:fillColor="#666666"
        android:x="20"
        android:y="8"
        android:width="2"
        android:height="8"
        android:rx="1" />
    <rect
        android:fillColor="#666666"
        android:x="26"
        android:y="8"
        android:width="2"
        android:height="8"
        android:rx="1" />
    <rect
        android:fillColor="#666666"
        android:x="32"
        android:y="8"
        android:width="2"
        android:height="8"
        android:rx="1" />
    
    <!-- Calendar grid -->
    <rect
        android:fillColor="#FFFFFF"
        android:x="12"
        android:y="22"
        android:width="4"
        android:height="4"
        android:rx="1" />
    <rect
        android:fillColor="#FFFFFF"
        android:x="18"
        android:y="22"
        android:width="4"
        android:height="4"
        android:rx="1" />
    <rect
        android:fillColor="#FFFFFF"
        android:x="24"
        android:y="22"
        android:width="4"
        android:height="4"
        android:rx="1" />
    <rect
        android:fillColor="#FFFFFF"
        android:x="30"
        android:y="22"
        android:width="4"
        android:height="4"
        android:rx="1" />
    
    <rect
        android:fillColor="#FFFFFF"
        android:x="12"
        android:y="28"
        android:width="4"
        android:height="4"
        android:rx="1" />
    <rect
        android:fillColor="#FFFFFF"
        android:x="18"
        android:y="28"
        android:width="4"
        android:height="4"
        android:rx="1" />
    <rect
        android:fillColor="#FFFFFF"
        android:x="24"
        android:y="28"
        android:width="4"
        android:height="4"
        android:rx="1" />
    
    <!-- Highlighted date -->
    <circle
        android:fillColor="#FFD54F"
        android:cx="20"
        android:cy="30"
        android:r="3" />
</vector>
