<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".FirstFragment">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="@dimen/box_margin">

        <!-- First row: Tickets and FAQs -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="@dimen/box_margin">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_tickets"
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_weight="1"
                android:layout_marginEnd="@dimen/box_margin"
                android:clickable="true"
                android:focusable="true"
                app:cardBackgroundColor="@color/box_tickets_bg"
                app:cardCornerRadius="@dimen/box_corner_radius"
                app:cardElevation="@dimen/box_elevation"
                app:rippleColor="@color/icon_tickets">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="@dimen/box_padding">

                    <ImageView
                        android:layout_width="@dimen/box_icon_size"
                        android:layout_height="@dimen/box_icon_size"
                        android:src="@drawable/ic_tickets"
                        android:contentDescription="@string/box_tickets" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/box_tickets"
                        android:textSize="@dimen/box_title_text_size"
                        android:textStyle="bold"
                        android:textColor="@color/icon_tickets"
                        android:layout_marginTop="8dp" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_faqs"
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_weight="1"
                android:clickable="true"
                android:focusable="true"
                app:cardBackgroundColor="@color/box_faqs_bg"
                app:cardCornerRadius="@dimen/box_corner_radius"
                app:cardElevation="@dimen/box_elevation"
                app:rippleColor="@color/icon_faqs">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="@dimen/box_padding">

                    <ImageView
                        android:layout_width="@dimen/box_icon_size"
                        android:layout_height="@dimen/box_icon_size"
                        android:src="@drawable/ic_faqs"
                        android:contentDescription="@string/box_faqs" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/box_faqs"
                        android:textSize="@dimen/box_title_text_size"
                        android:textStyle="bold"
                        android:textColor="@color/icon_faqs"
                        android:layout_marginTop="8dp" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>

        <!-- Second row: Knowledges and Events -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="@dimen/box_margin">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_knowledges"
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_weight="1"
                android:layout_marginEnd="@dimen/box_margin"
                android:clickable="true"
                android:focusable="true"
                app:cardBackgroundColor="@color/box_knowledges_bg"
                app:cardCornerRadius="@dimen/box_corner_radius"
                app:cardElevation="@dimen/box_elevation"
                app:rippleColor="@color/icon_knowledges">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="@dimen/box_padding">

                    <ImageView
                        android:layout_width="@dimen/box_icon_size"
                        android:layout_height="@dimen/box_icon_size"
                        android:src="@drawable/ic_knowledges"
                        android:contentDescription="@string/box_knowledges" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/box_knowledges"
                        android:textSize="@dimen/box_title_text_size"
                        android:textStyle="bold"
                        android:textColor="@color/icon_knowledges"
                        android:layout_marginTop="8dp" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_events"
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_weight="1"
                android:clickable="true"
                android:focusable="true"
                app:cardBackgroundColor="@color/box_events_bg"
                app:cardCornerRadius="@dimen/box_corner_radius"
                app:cardElevation="@dimen/box_elevation"
                app:rippleColor="@color/icon_events">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="@dimen/box_padding">

                    <ImageView
                        android:layout_width="@dimen/box_icon_size"
                        android:layout_height="@dimen/box_icon_size"
                        android:src="@drawable/ic_events"
                        android:contentDescription="@string/box_events" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/box_events"
                        android:textSize="@dimen/box_title_text_size"
                        android:textStyle="bold"
                        android:textColor="@color/icon_events"
                        android:layout_marginTop="8dp" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>

        <!-- Third row: Profile and Users -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_profile"
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_weight="1"
                android:layout_marginEnd="@dimen/box_margin"
                android:clickable="true"
                android:focusable="true"
                app:cardBackgroundColor="@color/box_profile_bg"
                app:cardCornerRadius="@dimen/box_corner_radius"
                app:cardElevation="@dimen/box_elevation"
                app:rippleColor="@color/icon_profile">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="@dimen/box_padding">

                    <ImageView
                        android:layout_width="@dimen/box_icon_size"
                        android:layout_height="@dimen/box_icon_size"
                        android:src="@drawable/ic_profile"
                        android:contentDescription="@string/box_profile" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/box_profile"
                        android:textSize="@dimen/box_title_text_size"
                        android:textStyle="bold"
                        android:textColor="@color/icon_profile"
                        android:layout_marginTop="8dp" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>

            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_users"
                android:layout_width="0dp"
                android:layout_height="120dp"
                android:layout_weight="1"
                android:clickable="true"
                android:focusable="true"
                app:cardBackgroundColor="@color/box_users_bg"
                app:cardCornerRadius="@dimen/box_corner_radius"
                app:cardElevation="@dimen/box_elevation"
                app:rippleColor="@color/icon_users">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical"
                    android:gravity="center"
                    android:padding="@dimen/box_padding">

                    <ImageView
                        android:layout_width="@dimen/box_icon_size"
                        android:layout_height="@dimen/box_icon_size"
                        android:src="@drawable/ic_users"
                        android:contentDescription="@string/box_users" />

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/box_users"
                        android:textSize="@dimen/box_title_text_size"
                        android:textStyle="bold"
                        android:textColor="@color/icon_users"
                        android:layout_marginTop="8dp" />
                </LinearLayout>
            </com.google.android.material.card.MaterialCardView>
        </LinearLayout>
    </LinearLayout>
</androidx.core.widget.NestedScrollView>
